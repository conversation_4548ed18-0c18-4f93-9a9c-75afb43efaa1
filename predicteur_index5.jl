"""
PRÉDICTEUR INDEX5 - DÉTERMINATION DES VALEURS POSSIBLES À LA MAIN N+1
=====================================================================

Programme qui détermine les 6 valeurs possibles d'INDEX5 à la main n+1
basé sur les règles de transition d'INDEX1 selon INDEX2.
Les possibilités TIE à l'INDEX3 sont exclues.

RÈGLES DE TRANSITION INDEX1 :
- Si INDEX1 = 0 et INDEX2 = C à la main n → INDEX1 = 1 à la main n+1
- Si INDEX1 = 1 et INDEX2 = C à la main n → INDEX1 = 0 à la main n+1
- Si INDEX1 = 0 et INDEX2 = A à la main n → INDEX1 = 0 à la main n+1
- Si INDEX1 = 1 et INDEX2 = A à la main n → INDEX1 = 1 à la main n+1
- Si INDEX1 = 0 et INDEX2 = B à la main n → INDEX1 = 0 à la main n+1
- Si INDEX1 = 1 et INDEX2 = B à la main n → INDEX1 = 1 à la main n+1

INDEX5 = INDEX1_INDEX2_INDEX3 (avec INDEX3 ∈ {BANKER, PLAYER})
"""

using JSON3  # JSON3.jl est 10x plus rapide et utilise 10x moins de mémoire que JSON.jl
using Printf
using Dates
using Statistics

# ═══════════════════════════════════════════════════════════════════════════════
# STRUCTURES ET TYPES
# ═══════════════════════════════════════════════════════════════════════════════

"""
    MainData

Structure pour stocker les données d'une main.
"""
struct MainData
    main_number::Union{Int, Nothing}
    manche_pb_number::Union{Int, Nothing}
    index1::Union{Int, String}
    index2::String
    index3::String
    index5::String
end

"""
    FormulasTheoretical{T<:AbstractFloat}

Structure contenant les probabilités théoriques INDEX5 et paramètres pour les calculs d'entropie.
"""
mutable struct FormulasTheoretical{T<:AbstractFloat}
    base::T
    epsilon::T
    theoretical_probs::Dict{String,T}
    sequence_complete::Vector{String}  # Séquence complète pour calculs empiriques

    function FormulasTheoretical{T}(base::T = T(2.0), epsilon::T = T(1e-12)) where T<:AbstractFloat
        # Probabilités théoriques INDEX5 (identiques à entropie_baccarat_analyzer.jl)
        theoretical_probs = Dict{String,T}(
            "0_A_BANKER" => T(0.085136), "1_A_BANKER" => T(0.086389),
            "0_B_BANKER" => T(0.064676), "1_B_BANKER" => T(0.065479),
            "0_C_BANKER" => T(0.077903), "1_C_BANKER" => T(0.078929),
            "0_A_PLAYER" => T(0.085240), "1_A_PLAYER" => T(0.086361),
            "0_B_PLAYER" => T(0.076907), "1_B_PLAYER" => T(0.077888),
            "0_C_PLAYER" => T(0.059617), "1_C_PLAYER" => T(0.060352),
            "0_A_TIE" => T(0.017719), "1_A_TIE" => T(0.017978),
            "0_B_TIE" => T(0.016281), "1_B_TIE" => T(0.016482),
            "0_C_TIE" => T(0.013241), "1_C_TIE" => T(0.013423)
        )
        new{T}(base, epsilon, theoretical_probs, String[])
    end
end

# Constructeur de convenance
FormulasTheoretical(base::Real = 2.0, epsilon::Real = 1e-12) =
    FormulasTheoretical{Float64}(Float64(base), Float64(epsilon))

"""
    MetriquesTheorique{T<:AbstractFloat}

Structure pour stocker les 10 métriques théoriques calculées.
ORDRE DE PRIORITÉ PRÉDICTIVE (du plus important au moins important) :
1. InfoMutT - Information Mutuelle (dépendance directe)
2. CondT - Entropie Conditionnelle (prévisibilité immédiate)
3. DivKLT - Divergence KL (biais du modèle)
4. CrossT - Entropie Croisée (inefficacité du modèle)
5. MetricT - Entropie Métrique (variation de complexité)
6. TopoT - Entropie Topologique (patterns multi-échelles)
7. TauxT - Taux d'Entropie (complexité normalisée)
8. ShannonT - Entropie de Shannon (diversité observée)
9. BlockT - Entropie Jointe (complexité totale)
"""
struct MetriquesTheorique{T<:AbstractFloat}
    cond_t::T         # 1. CondT - PRIORITÉ 1
    divkl_t::T        # 2. DivKLT - PRIORITÉ 2
    cross_t::T        # 3. CrossT - PRIORITÉ 2
    metric_t::T       # 4. MetricT - PRIORITÉ 3
    topo_t::T         # 5. TopoT - PRIORITÉ 3
    taux_t::T         # 6. TauxT - PRIORITÉ 4
    shannon_t::T      # 7. ShannonT - PRIORITÉ 5
    block_t::T        # 8. BlockT - PRIORITÉ 5
end

"""
    DifferentielsPredictifs{T<:AbstractFloat}

Structure pour stocker les différentiels des 10 métriques théoriques pour la prédiction.
Calcule |métrique(n+1) - métrique(n)| pour chacune des 6 possibilités à la main n+1.
ORDRE DE PRIORITÉ PRÉDICTIVE (du plus important au moins important) :
"""
struct DifferentielsPredictifs{T<:AbstractFloat}
    # Différentiels des 8 métriques théoriques (ordre de priorité prédictive)
    diff_cond_t::T         # 1. |CondT(n+1) - CondT(n)| - PRIORITÉ 1
    diff_divkl_t::T        # 2. |DivKLT(n+1) - DivKLT(n)| - PRIORITÉ 2
    diff_cross_t::T        # 3. |CrossT(n+1) - CrossT(n)| - PRIORITÉ 2
    diff_metric_t::T       # 4. |MetricT(n+1) - MetricT(n)| - PRIORITÉ 3
    diff_topo_t::T         # 5. |TopoT(n+1) - TopoT(n)| - PRIORITÉ 3
    diff_taux_t::T         # 6. |TauxT(n+1) - TauxT(n)| - PRIORITÉ 4
    diff_shannon_t::T      # 7. |ShannonT(n+1) - ShannonT(n)| - PRIORITÉ 5
    diff_block_t::T        # 8. |BlockT(n+1) - BlockT(n)| - PRIORITÉ 5
end

"""
    CalculateurDifferentielsPredictifs{T<:AbstractFloat}

Classe pour calculer les différentiels prédictifs des métriques théoriques.
"""
struct CalculateurDifferentielsPredictifs{T<:AbstractFloat}
    formulas::FormulasTheoretical{T}

    function CalculateurDifferentielsPredictifs{T}(base::T = T(2.0), epsilon::T = T(1e-12)) where T<:AbstractFloat
        formulas = FormulasTheoretical{T}(base, epsilon)
        new{T}(formulas)
    end
end

# Constructeur de convenance
CalculateurDifferentielsPredictifs(base::Real = 2.0, epsilon::Real = 1e-12) =
    CalculateurDifferentielsPredictifs{Float64}(Float64(base), Float64(epsilon))

"""
    PredictionResult

Structure pour stocker le résultat de prédiction pour une main avec métriques et différentiels.
"""
struct PredictionResult
    main_actuelle::Int
    index5_actuel::String
    index1_suivant::Int
    index5_observe::Union{String, Nothing}  # INDEX5 réellement observé à la main n+1
    valeurs_possibles::Vector{String}
    metriques_par_possibilite::Vector{MetriquesTheorique{Float64}}
    differentiels_par_possibilite::Vector{DifferentielsPredictifs{Float64}}
end

# ═══════════════════════════════════════════════════════════════════════════════
# FONCTIONS UTILITAIRES
# ═══════════════════════════════════════════════════════════════════════════════

"""
    trouver_fichier_json_recent(dossier::String) -> String

Trouve le fichier JSON le plus récent dans le dossier spécifié.
"""
function trouver_fichier_json_recent(dossier::String)
    if !isdir(dossier)
        throw(ArgumentError("Le dossier '$dossier' n'existe pas"))
    end
    
    fichiers_json = filter(f -> endswith(f, ".json"), readdir(dossier))
    
    if isempty(fichiers_json)
        throw(ArgumentError("Aucun fichier JSON trouvé dans le dossier '$dossier'"))
    end
    
    # Trier par date de modification (plus récent en premier)
    fichiers_avec_dates = [(f, stat(joinpath(dossier, f)).mtime) for f in fichiers_json]
    sort!(fichiers_avec_dates, by=x->x[2], rev=true)
    
    fichier_recent = fichiers_avec_dates[1][1]
    chemin_complet = joinpath(dossier, fichier_recent)
    
    println("📁 Fichier JSON le plus récent trouvé : $fichier_recent")
    return chemin_complet
end

"""
    charger_donnees_partie(chemin_fichier::String, numero_partie::Int) -> Vector{MainData}

Charge les données d'une partie spécifique depuis un fichier JSON.
"""
function charger_donnees_partie(chemin_fichier::String, numero_partie::Int)
    println("📖 Chargement du fichier : $chemin_fichier")
    println("🎯 Recherche de la partie numéro : $numero_partie")

    try
        contenu = read(chemin_fichier, String)
        donnees = JSON.parse(contenu)

        if !haskey(donnees, "parties_condensees") || isempty(donnees["parties_condensees"])
            throw(ArgumentError("Format de fichier invalide : 'parties_condensees' manquant ou vide"))
        end

        # Chercher la partie demandée
        partie_trouvee = nothing
        for partie in donnees["parties_condensees"]
            if partie["partie_number"] == numero_partie
                partie_trouvee = partie
                break
            end
        end

        if partie_trouvee === nothing
            throw(ArgumentError("Partie numéro $numero_partie non trouvée"))
        end

        mains_data = partie_trouvee["mains_condensees"]

        # Convertir en structures MainData
        mains = MainData[]

        for main_data in mains_data
            # Ignorer les mains avec des données manquantes
            if main_data["main_number"] === nothing ||
               main_data["index1"] === "" ||
               main_data["index2"] === "" ||
               main_data["index3"] === ""
                continue
            end

            push!(mains, MainData(
                main_data["main_number"],
                main_data["manche_pb_number"],
                main_data["index1"],
                main_data["index2"],
                main_data["index3"],
                main_data["index5"]
            ))
        end

        println("✅ Partie $numero_partie chargée : $(length(mains)) mains valides")
        println("📊 Statistiques de la partie :")
        if haskey(partie_trouvee, "statistiques")
            stats = partie_trouvee["statistiques"]
            println("   • Total mains : $(stats["total_mains"])")
            println("   • Manches P/B : $(stats["total_manches_pb"])")
            println("   • Ties : $(stats["total_ties"])")
        end

        return mains

    catch e
        println("❌ Erreur lors du chargement : $e")
        rethrow(e)
    end
end

"""
    compter_parties_disponibles(chemin_fichier::String) -> Int

Compte le nombre de parties disponibles dans le fichier JSON.
"""
function compter_parties_disponibles(chemin_fichier::String)
    try
        contenu = read(chemin_fichier, String)
        donnees = JSON.parse(contenu)

        if !haskey(donnees, "parties_condensees")
            return 0
        end

        return length(donnees["parties_condensees"])

    catch e
        println("❌ Erreur lors du comptage des parties : $e")
        return 0
    end
end

# ═══════════════════════════════════════════════════════════════════════════════
# CALCULS DES MÉTRIQUES THÉORIQUES
# ═══════════════════════════════════════════════════════════════════════════════

"""
    calculer_formule1B_shannon_jointe_theo(formulas::FormulasTheoretical{T}, sequence::Vector{String}, n::Int) where T -> T

FORMULE 1B : Entropie de Shannon Jointe (VERSION THÉORIQUE)
Calcule l'entropie jointe théorique pour la fenêtre croissante de la main 1 à la main n.
"""
function calculer_formule1B_shannon_jointe_theo(
    formulas::FormulasTheoretical{T},
    sequence::Vector{String},
    n::Int
) where T<:AbstractFloat
    if n <= 0 || n > length(sequence)
        return zero(T)
    end

    # Extraire la sous-séquence jusqu'à la main n
    subsequence = sequence[1:n]

    # Compter les occurrences observées dans la séquence
    counts = Dict{String, Int}()
    for value in subsequence
        counts[value] = get(counts, value, 0) + 1
    end

    # Calculer l'entropie de Shannon théorique basée sur les probabilités INDEX5
    entropy = zero(T)
    total = length(subsequence)

    # Pour chaque valeur unique observée, utiliser sa probabilité théorique INDEX5
    for (value, count) in counts
        p_theo = get(formulas.theoretical_probs, value, formulas.epsilon)
        if p_theo > zero(T)
            # Entropie de Shannon pure : H(X) = -∑ p(x) log₂(p(x))
            # Formule correcte selon cours_entropie/niveau_debutant/02_formule_shannon.md
            # Caractères : H = -∑ pᵢ × log₂(pᵢ)
            entropy -= p_theo * (log(p_theo) / log(formulas.base))
        end
    end

    return entropy
end



"""
    calculer_formule3B_taux_entropie_theo(formulas::FormulasTheoretical{T}, sequence::Vector{String}, n::Int) where T -> T

FORMULE 3B : Taux d'Entropie (VERSION OPTIMISÉE)
Calcule le taux d'entropie théorique en utilisant l'entropie de bloc.
Formule : TauxT_n = (1/n) × BlockT_n = (1/n) × ∑ᵢ₌₁ⁿ H(Xᵢ|X₁,...,Xᵢ₋₁)
Relation directe avec BlockT pour éviter la duplication de code.
"""
function calculer_formule3B_taux_entropie_theo(
    formulas::FormulasTheoretical{T},
    sequence::Vector{String},
    n::Int
) where T<:AbstractFloat
    if n <= 0 || n > length(sequence)
        return zero(T)
    end

    # Calculer l'entropie de bloc et la diviser par n
    block_entropy = calculer_formule10B_block_cumulative_theo(formulas, sequence, n)

    # Retourner le taux d'entropie : TauxT_n = BlockT_n / n
    return block_entropy / T(n)
end

"""
    calculer_probabilite_conditionnelle_theo(formulas::FormulasTheoretical{T}, current_value::String, context::Vector{String}) where T -> T

Calcule la vraie probabilité conditionnelle théorique p_theo(xᵢ|x₁,...,xᵢ₋₁)
Sans hypothèse d'indépendance - utilise les probabilités jointes théoriques.
Formule : p(xᵢ|x₁,...,xᵢ₋₁) = p(x₁,...,xᵢ) / p(x₁,...,xᵢ₋₁)
"""
function calculer_probabilite_conditionnelle_theo(
    formulas::FormulasTheoretical{T},
    current_value::String,
    context::Vector{String}
) where T<:AbstractFloat

    # Construire la séquence complète : context + current_value
    full_sequence = vcat(context, [current_value])

    # Calculer p_theo(x₁,...,xᵢ) - probabilité jointe de la séquence complète
    p_joint_full = calculer_probabilite_jointe_theo(formulas, full_sequence)

    # Calculer p_theo(x₁,...,xᵢ₋₁) - probabilité jointe du contexte
    p_joint_context = calculer_probabilite_jointe_theo(formulas, context)

    # p_theo(xᵢ|x₁,...,xᵢ₋₁) = p_theo(x₁,...,xᵢ) / p_theo(x₁,...,xᵢ₋₁)
    if p_joint_context > zero(T)
        return p_joint_full / p_joint_context
    else
        # Si le contexte a une probabilité nulle, utiliser la probabilité marginale
        return get(formulas.theoretical_probs, current_value, formulas.epsilon)
    end
end

"""
    calculer_probabilite_jointe_theo(formulas::FormulasTheoretical{T}, sequence::Vector{String}) where T -> T

Calcule la probabilité jointe théorique d'une séquence en utilisant les probabilités conditionnelles
empiriques basées sur les observations de la séquence complète.
Pour une séquence de longueur 1, retourne p_theo(x₁).
Pour une séquence plus longue, utilise la règle de chaîne avec les probabilités conditionnelles empiriques.
"""
function calculer_probabilite_jointe_theo(
    formulas::FormulasTheoretical{T},
    sequence::Vector{String}
) where T<:AbstractFloat

    if isempty(sequence)
        return zero(T)
    end

    if length(sequence) == 1
        # p_theo(x₁) - utiliser la probabilité théorique
        return get(formulas.theoretical_probs, sequence[1], formulas.epsilon)
    end

    # Pour les séquences plus longues, utiliser les probabilités conditionnelles empiriques
    # basées sur les observations dans la séquence complète disponible

    joint_prob = one(T)

    # Premier terme : p_theo(x₁) - probabilité théorique du premier élément
    first_value = sequence[1]
    p_first = get(formulas.theoretical_probs, first_value, formulas.epsilon)
    joint_prob *= p_first

    # Termes suivants : p_empirique(xᵢ|xᵢ₋₁) basé sur les observations
    for i in 2:length(sequence)
        current_value = sequence[i]
        previous_value = sequence[i-1]

        # Calculer p_empirique(xᵢ|xᵢ₋₁) basé sur les observations dans la séquence complète
        p_conditional_empirique = calculer_probabilite_conditionnelle_empirique(
            formulas, current_value, previous_value
        )

        joint_prob *= p_conditional_empirique
    end

    return joint_prob
end

"""
    calculer_probabilite_conditionnelle_empirique(formulas::FormulasTheoretical{T}, current_value::String, previous_value::String) where T -> T

Calcule la probabilité conditionnelle empirique p_empirique(xᵢ|xᵢ₋₁) basée sur les observations
dans la séquence complète disponible dans formulas.
Formule : p_empirique(xᵢ|xᵢ₋₁) = count(xᵢ₋₁ → xᵢ) / count(xᵢ₋₁)
"""
function calculer_probabilite_conditionnelle_empirique(
    formulas::FormulasTheoretical{T},
    current_value::String,
    previous_value::String
) where T<:AbstractFloat

    # Accéder à la séquence complète stockée dans formulas
    # Si la séquence complète n'est pas disponible, utiliser une approximation
    if !hasfield(typeof(formulas), :sequence_complete) || isempty(formulas.sequence_complete)
        # Fallback : utiliser la probabilité théorique marginale
        return get(formulas.theoretical_probs, current_value, formulas.epsilon)
    end

    sequence_complete = formulas.sequence_complete

    # Compter les transitions previous_value → current_value
    count_transition = 0
    count_previous = 0

    for i in 1:(length(sequence_complete) - 1)
        if sequence_complete[i] == previous_value
            count_previous += 1
            if sequence_complete[i + 1] == current_value
                count_transition += 1
            end
        end
    end

    # Calculer p_empirique(current|previous) = count(previous → current) / count(previous)
    if count_previous > 0
        return T(count_transition) / T(count_previous)
    else
        # Si previous_value n'a jamais été observé, utiliser la probabilité théorique marginale
        return get(formulas.theoretical_probs, current_value, formulas.epsilon)
    end
end

"""
    calculer_formule4B_entropie_metrique_theo(formulas::FormulasTheoretical{T}, sequence::Vector{String}, n::Int) where T -> T

FORMULE 4B : Entropie Métrique Kolmogorov-Sinai (VERSION THÉORIQUE CORRIGÉE)
Entropie métrique pondérée : mesure l'impact de l'ajout du n-ème élément sur la complexité informationnelle pondérée globale.
Formule : MetricT_n = [(2/(n(n+1))) × ∑ᵢ₌₁ⁿ i × H_theo(Xᵢ | X₁,...,Xᵢ₋₁)] - [(2/((n-1)n)) × ∑ᵢ₌₁ⁿ⁻¹ i × H_theo(Xᵢ | X₁,...,Xᵢ₋₁)]
"""
function calculer_formule4B_entropie_metrique_theo(
    formulas::FormulasTheoretical{T},
    sequence::Vector{String},
    n::Int
) where T<:AbstractFloat
    if n <= 1 || n > length(sequence)
        return zero(T)
    end

    # Fonction auxiliaire pour calculer H_theo(Xᵢ | X₁,...,Xᵢ₋₁)
    function calculer_entropie_conditionnelle(i::Int)
        if i == 1
            # H_theo(X₁) - pas de conditionnement
            value = sequence[i]
            p_theo = get(formulas.theoretical_probs, value, formulas.epsilon)
            return p_theo > zero(T) ? -(log(p_theo) / log(formulas.base)) : zero(T)
        else
            # H_theo(Xᵢ | X₁,...,Xᵢ₋₁) = H_theo(X₁,...,Xᵢ) - H_theo(X₁,...,Xᵢ₋₁)
            h_i = calculer_formule1B_shannon_jointe_theo(formulas, sequence, i)
            h_i_minus_1 = calculer_formule1B_shannon_jointe_theo(formulas, sequence, i-1)
            return h_i - h_i_minus_1
        end
    end

    # ÉTAPE 1 - Calcul pour la séquence [1:n-1]
    # Complexité_pondérée(n-1) = (2/((n-1)n)) × ∑ᵢ₌₁ⁿ⁻¹ i × H_theo(Xᵢ | X₁,...,Xᵢ₋₁)
    somme_ponderee_n_minus_1 = zero(T)
    for i in 1:(n-1)
        h_cond = calculer_entropie_conditionnelle(i)
        somme_ponderee_n_minus_1 += T(i) * h_cond
    end
    complexite_n_minus_1 = (T(2) / (T(n-1) * T(n))) * somme_ponderee_n_minus_1

    # ÉTAPE 2 - Calcul pour la séquence [1:n]
    # Complexité_pondérée(n) = (2/(n(n+1))) × ∑ᵢ₌₁ⁿ i × H_theo(Xᵢ | X₁,...,Xᵢ₋₁)
    somme_ponderee_n = zero(T)
    for i in 1:n
        h_cond = calculer_entropie_conditionnelle(i)
        somme_ponderee_n += T(i) * h_cond
    end
    complexite_n = (T(2) / (T(n) * T(n+1))) * somme_ponderee_n

    # ÉTAPE 3 - DIFFÉRENCE (ENTROPIE MÉTRIQUE PONDÉRÉE)
    # MetricT_n = Complexité_pondérée(n) - Complexité_pondérée(n-1)
    return complexite_n - complexite_n_minus_1
end

"""
    calculer_formule5B_conditionnelle_theo(formulas::FormulasTheoretical{T}, sequence::Vector{String}, n::Int) where T -> T

FORMULE 5B : Entropie Conditionnelle Cumulative (VERSION THÉORIQUE CORRIGÉE)
Entropie conditionnelle cumulative moyenne de toute la séquence [1:n] selon la Chain Rule.
Formule : CondT_n = (1/n) × ∑ᵢ₌₁ⁿ H_theo(Xᵢ | X₁,...,Xᵢ₋₁)
Relation : CondT_n = H_theo(X₁,...,Xₙ) / n
Signification : Prévisibilité globale du système INDEX5
"""
function calculer_formule5B_conditionnelle_theo(
    formulas::FormulasTheoretical{T},
    sequence::Vector{String},
    n::Int
) where T<:AbstractFloat
    if n <= 0 || n > length(sequence)
        return zero(T)
    end

    # Fonction auxiliaire pour calculer H_theo(Xᵢ | X₁,...,Xᵢ₋₁)
    function calculer_entropie_conditionnelle(i::Int)
        if i == 1
            # H_theo(X₁) - pas de conditionnement
            value = sequence[i]
            p_theo = get(formulas.theoretical_probs, value, formulas.epsilon)
            return p_theo > zero(T) ? -(log(p_theo) / log(formulas.base)) : zero(T)
        else
            # H_theo(Xᵢ | X₁,...,Xᵢ₋₁) = H_theo(X₁,...,Xᵢ) - H_theo(X₁,...,Xᵢ₋₁)
            h_i = calculer_formule1B_shannon_jointe_theo(formulas, sequence, i)
            h_i_minus_1 = calculer_formule1B_shannon_jointe_theo(formulas, sequence, i-1)
            return h_i - h_i_minus_1
        end
    end

    # Calculer la somme des entropies conditionnelles : ∑ᵢ₌₁ⁿ H_theo(Xᵢ | X₁,...,Xᵢ₋₁)
    somme_entropies_conditionnelles = zero(T)
    for i in 1:n
        h_cond = calculer_entropie_conditionnelle(i)
        somme_entropies_conditionnelles += h_cond
    end

    # Retourner la moyenne : CondT_n = (1/n) × ∑ᵢ₌₁ⁿ H_theo(Xᵢ | X₁,...,Xᵢ₋₁)
    return somme_entropies_conditionnelles / T(n)
end

"""
    calculer_formule6B_divergence_kl_theo(formulas::FormulasTheoretical{T}, sequence::Vector{String}, n::Int) where T -> T

FORMULE 6B : Divergence KL Observée vs Théorique (VERSION CORRIGÉE)
Mesure l'écart entre les fréquences réellement observées et les probabilités théoriques INDEX5.
Formule : DivKLT = ∑ p_obs(x) × log₂(p_obs(x)/p_theo(x))
Usage standard : p_obs = distribution "vraie" (observée), p_theo = distribution "approximative" (modèle)
"""
function calculer_formule6B_divergence_kl_theo(
    formulas::FormulasTheoretical{T},
    sequence::Vector{String},
    n::Int
) where T<:AbstractFloat
    if n <= 0 || n > length(sequence)
        return zero(T)
    end

    # Extraire la sous-séquence jusqu'à la main n
    subsequence = sequence[1:n]

    # Compter les occurrences observées
    counts = Dict{String, Int}()
    for value in subsequence
        counts[value] = get(counts, value, 0) + 1
    end

    # Calculer la divergence KL : ∑ p_obs(x) × log₂(p_obs(x)/p_theo(x))
    divergence = zero(T)
    n_total = T(length(subsequence))

    for (value, count) in counts
        # Calculer p_obs(x) = count(x dans séquence [1:n]) / n
        p_obs = T(count) / n_total

        # Récupérer p_theo(x) depuis le modèle INDEX5
        p_theo = get(formulas.theoretical_probs, value, formulas.epsilon)

        if p_obs > zero(T) && p_theo > zero(T)
            # Divergence KL standard : p_obs × log₂(p_obs/p_theo)
            divergence += p_obs * (log(p_obs / p_theo) / log(formulas.base))
        end
    end

    return divergence
end



"""
    calculer_formule8B_entropie_croisee_theo(formulas::FormulasTheoretical{T}, sequence::Vector{String}, n::Int) where T -> T

FORMULE 8B : Entropie Croisée Observée vs Théorique (VERSION CORRIGÉE)
Mesure le coût d'encodage des données réellement observées en utilisant les probabilités théoriques INDEX5 comme modèle de codage.
Formule : CrossT = -∑ p_obs(x) × log₂ p_theo(x)
Usage standard : p_obs = distribution "vraie" (observée), p_theo = distribution "de codage" (modèle)
"""
function calculer_formule8B_entropie_croisee_theo(
    formulas::FormulasTheoretical{T},
    sequence::Vector{String},
    n::Int
) where T<:AbstractFloat
    if n <= 0 || n > length(sequence)
        return zero(T)
    end

    # Extraire la sous-séquence jusqu'à la main n
    subsequence = sequence[1:n]

    # Compter les occurrences observées
    counts = Dict{String, Int}()
    for value in subsequence
        counts[value] = get(counts, value, 0) + 1
    end

    # Calculer l'entropie croisée : -∑ p_obs(x) × log₂ p_theo(x)
    cross_entropy = zero(T)
    n_total = T(length(subsequence))

    for (value, count) in counts
        # Calculer p_obs(x) = count(x dans séquence [1:n]) / n
        p_obs = T(count) / n_total

        # Récupérer p_theo(x) depuis le modèle INDEX5
        p_theo = get(formulas.theoretical_probs, value, formulas.epsilon)

        if p_obs > zero(T) && p_theo > zero(T)
            # Entropie croisée standard : -p_obs × log₂(p_theo)
            cross_entropy -= p_obs * (log(p_theo) / log(formulas.base))
        end
    end

    return cross_entropy
end

"""
    calculer_formule9B_entropie_topologique_theo(formulas::FormulasTheoretical{T}, sequence::Vector{String}, n::Int) where T -> T

FORMULE 9B : Entropie Topologique Multi-Échelles (VERSION THÉORIQUE CORRIGÉE)
Entropie topologique multi-échelles basée sur la complexité théorique INDEX5 à 3 niveaux de résolution.
Formule : TopoT_n = 0.167 × H_theo(blocs_1) + 0.333 × H_theo(blocs_2) + 0.500 × H_theo(blocs_3)
Pondération basée sur la capacité informationnelle théorique de chaque échelle.
"""
function calculer_formule9B_entropie_topologique_theo(
    formulas::FormulasTheoretical{T},
    sequence::Vector{String},
    n::Int
) where T<:AbstractFloat
    if n <= 0 || n > length(sequence)
        return zero(T)
    end

    # Extraire la sous-séquence jusqu'à la main n
    subsequence = sequence[1:n]

    # Fonction auxiliaire pour calculer H_theo(blocs_k) avec probabilités théoriques INDEX5
    function calculer_entropie_blocs(block_size::Int)
        if block_size > length(subsequence)
            return zero(T)
        end

        # Extraire tous les blocs de taille block_size
        blocs_distincts = Set{Vector{String}}()
        for i in 1:(length(subsequence) - block_size + 1)
            bloc = subsequence[i:i+block_size-1]
            push!(blocs_distincts, bloc)
        end

        # Calculer l'entropie théorique des blocs distincts
        entropy = zero(T)
        for bloc in blocs_distincts
            # Calculer la probabilité théorique du bloc sous hypothèse d'indépendance
            p_bloc_theo = one(T)
            for value in bloc
                p_value_theo = get(formulas.theoretical_probs, value, formulas.epsilon)
                p_bloc_theo *= p_value_theo
            end

            if p_bloc_theo > zero(T)
                entropy -= p_bloc_theo * (log(p_bloc_theo) / log(formulas.base))
            end
        end

        return entropy
    end

    # Calculer les entropies pour chaque échelle
    h_blocs_1 = calculer_entropie_blocs(1)  # Valeurs individuelles
    h_blocs_2 = n >= 2 ? calculer_entropie_blocs(2) : zero(T)  # Paires consécutives
    h_blocs_3 = n >= 3 ? calculer_entropie_blocs(3) : zero(T)  # Triplets consécutifs

    # Poids théoriques basés sur la capacité informationnelle
    w1 = T(0.167)  # 16.7% pour complexité locale
    w2 = T(0.333)  # 33.3% pour complexité des transitions
    w3 = T(0.500)  # 50.0% pour complexité des motifs

    # Entropie topologique multi-échelles pondérée
    topo_entropy = w1 * h_blocs_1 + w2 * h_blocs_2 + w3 * h_blocs_3

    return topo_entropy
end

"""
    calculer_formule10B_block_cumulative_theo(formulas::FormulasTheoretical{T}, sequence::Vector{String}, n::Int) where T -> T

FORMULE 10B : Entropie de Bloc Vraie (VERSION CORRIGÉE SELON DOCUMENTATION)
Calcule la vraie entropie jointe selon la règle de chaîne généralisée.
Formule : BlockT_n = H(X₁,...,Xₙ) = ∑ᵢ₌₁ⁿ H(Xᵢ|X₁,...,Xᵢ₋₁)
Basée sur la règle de chaîne : p(x₁,...,xₙ) = p(x₁) × ∏ᵢ₌₂ⁿ p(xᵢ|x₁,...,xᵢ₋₁)
"""
function calculer_formule10B_block_cumulative_theo(
    formulas::FormulasTheoretical{T},
    sequence::Vector{String},
    n::Int
) where T<:AbstractFloat
    if n <= 0 || n > length(sequence)
        return zero(T)
    end

    # Calculer l'entropie jointe selon la règle de chaîne généralisée :
    # H(X₁,...,Xₙ) = ∑ᵢ₌₁ⁿ H(Xᵢ|X₁,...,Xᵢ₋₁)

    total_joint_entropy = zero(T)

    for i in 1:n
        if i == 1
            # H(X₁) = -log₂(p_theo(x₁))
            first_value = sequence[1]
            p_first = get(formulas.theoretical_probs, first_value, formulas.epsilon)
            if p_first > zero(T)
                total_joint_entropy += -(log(p_first) / log(formulas.base))
            end
        else
            # H(Xᵢ|X₁,...,Xᵢ₋₁) = -log₂(p_theo(xᵢ|x₁,...,xᵢ₋₁))
            current_value = sequence[i]
            context = sequence[1:i-1]

            # Calculer la vraie probabilité conditionnelle p_theo(xᵢ|x₁,...,xᵢ₋₁)
            p_conditional = calculer_probabilite_conditionnelle_theo(
                formulas, current_value, context
            )

            if p_conditional > zero(T)
                total_joint_entropy += -(log(p_conditional) / log(formulas.base))
            end
        end
    end

    return total_joint_entropy
end





"""
    calculer_toutes_metriques_theoriques(formulas::FormulasTheoretical{T}, sequence::Vector{String}, n::Int) where T -> MetriquesTheorique{T}

Calcule toutes les 10 métriques théoriques pour une séquence donnée jusqu'à la main n.
"""
function calculer_toutes_metriques_theoriques(
    formulas::FormulasTheoretical{T},
    sequence::Vector{String},
    n::Int
) where T<:AbstractFloat

    # Initialiser la séquence complète pour les calculs de probabilités conditionnelles empiriques
    formulas.sequence_complete = sequence

    # Calcul des 8 métriques dans l'ordre de priorité prédictive (InfoMutT et CondDecT supprimés)
    cond_t = calculer_formule5B_conditionnelle_theo(formulas, sequence, n)              # 1. PRIORITÉ 1
    divkl_t = calculer_formule6B_divergence_kl_theo(formulas, sequence, n)              # 2. PRIORITÉ 2
    cross_t = calculer_formule8B_entropie_croisee_theo(formulas, sequence, n)           # 3. PRIORITÉ 2
    metric_t = calculer_formule4B_entropie_metrique_theo(formulas, sequence, n)         # 4. PRIORITÉ 3
    topo_t = calculer_formule9B_entropie_topologique_theo(formulas, sequence, n)        # 5. PRIORITÉ 3
    taux_t = calculer_formule3B_taux_entropie_theo(formulas, sequence, n)               # 6. PRIORITÉ 4
    shannon_t = calculer_formule1B_shannon_jointe_theo(formulas, sequence, n)           # 7. PRIORITÉ 5
    block_t = calculer_formule10B_block_cumulative_theo(formulas, sequence, n)          # 8. PRIORITÉ 5

    return MetriquesTheorique{T}(
        cond_t, divkl_t, cross_t, metric_t,
        topo_t, taux_t, shannon_t, block_t
    )
end

# ═══════════════════════════════════════════════════════════════════════════════
# CALCUL DES DIFFÉRENTIELS PRÉDICTIFS
# ═══════════════════════════════════════════════════════════════════════════════

"""
    calculer_differentiels_predictifs(calculateur::CalculateurDifferentielsPredictifs{T}, metriques_n::MetriquesTheorique{T}, metriques_n_plus_1::MetriquesTheorique{T}) where T -> DifferentielsPredictifs{T}

Calcule les différentiels prédictifs entre les métriques de la main n et n+1.
Formule : |métrique(n+1) - métrique(n)| pour chacune des 10 métriques théoriques.
"""
function calculer_differentiels_predictifs(
    calculateur::CalculateurDifferentielsPredictifs{T},
    metriques_n::MetriquesTheorique{T},
    metriques_n_plus_1::MetriquesTheorique{T}
) where T<:AbstractFloat

    # Calculer les différentiels absolus |métrique(n+1) - métrique(n)| (ordre de priorité prédictive, InfoMutT et CondDecT supprimés)
    diff_cond_t = abs(metriques_n_plus_1.cond_t - metriques_n.cond_t)             # 1. PRIORITÉ 1
    diff_divkl_t = abs(metriques_n_plus_1.divkl_t - metriques_n.divkl_t)          # 2. PRIORITÉ 2
    diff_cross_t = abs(metriques_n_plus_1.cross_t - metriques_n.cross_t)          # 3. PRIORITÉ 2
    diff_metric_t = abs(metriques_n_plus_1.metric_t - metriques_n.metric_t)       # 4. PRIORITÉ 3
    diff_topo_t = abs(metriques_n_plus_1.topo_t - metriques_n.topo_t)             # 5. PRIORITÉ 3
    diff_taux_t = abs(metriques_n_plus_1.taux_t - metriques_n.taux_t)             # 6. PRIORITÉ 4
    diff_shannon_t = abs(metriques_n_plus_1.shannon_t - metriques_n.shannon_t)    # 7. PRIORITÉ 5
    diff_block_t = abs(metriques_n_plus_1.block_t - metriques_n.block_t)          # 8. PRIORITÉ 5

    return DifferentielsPredictifs{T}(
        diff_cond_t, diff_divkl_t, diff_cross_t, diff_metric_t,
        diff_topo_t, diff_taux_t, diff_shannon_t, diff_block_t
    )
end

"""
    calculer_differentiels_pour_possibilites(calculateur::CalculateurDifferentielsPredictifs{T}, sequence_n::Vector{String}, valeurs_possibles::Vector{String}) where T -> Vector{DifferentielsPredictifs{T}}

Calcule les différentiels prédictifs pour toutes les possibilités à la main n+1.
"""
function calculer_differentiels_pour_possibilites(
    calculateur::CalculateurDifferentielsPredictifs{T},
    sequence_n::Vector{String},
    valeurs_possibles::Vector{String}
) where T<:AbstractFloat

    n = length(sequence_n)

    # Calculer les métriques pour la main n (état actuel)
    metriques_n = calculer_toutes_metriques_theoriques(calculateur.formulas, sequence_n, n)

    # Calculer les différentiels pour chaque possibilité
    differentiels = DifferentielsPredictifs{T}[]

    for valeur_possible in valeurs_possibles
        # Créer la séquence hypothétique avec cette valeur ajoutée
        sequence_n_plus_1 = vcat(sequence_n, [valeur_possible])

        # Calculer les métriques pour la main n+1 (état hypothétique)
        metriques_n_plus_1 = calculer_toutes_metriques_theoriques(calculateur.formulas, sequence_n_plus_1, n + 1)

        # Calculer les différentiels |métrique(n+1) - métrique(n)|
        diff = calculer_differentiels_predictifs(calculateur, metriques_n, metriques_n_plus_1)
        push!(differentiels, diff)
    end

    return differentiels
end

# ═══════════════════════════════════════════════════════════════════════════════
# LOGIQUE DE PRÉDICTION
# ═══════════════════════════════════════════════════════════════════════════════

"""
    calculer_index1_suivant(index1_actuel::Int, index2_actuel::String) -> Int

Calcule la valeur d'INDEX1 pour la main suivante selon les règles de transition.
"""
function calculer_index1_suivant(index1_actuel::Int, index2_actuel::String)
    if index2_actuel == "C"
        # Règle C : INDEX1 s'inverse
        return index1_actuel == 0 ? 1 : 0
    elseif index2_actuel == "A" || index2_actuel == "B"
        # Règles A et B : INDEX1 reste identique
        return index1_actuel
    else
        throw(ArgumentError("INDEX2 invalide : '$index2_actuel'. Doit être A, B ou C"))
    end
end

"""
    generer_valeurs_possibles(index1_suivant::Int) -> Vector{String}

Génère les 6 valeurs possibles d'INDEX5 pour la main suivante.
Exclut les possibilités TIE à l'INDEX3.
Ordre : d'abord tous les BANKER (A, B, C), puis tous les PLAYER (A, B, C).
"""
function generer_valeurs_possibles(index1_suivant::Int)
    index2_possibles = ["A", "B", "C"]
    index3_possibles = ["BANKER", "PLAYER"]  # TIE supprimé

    valeurs_possibles = String[]

    # D'abord tous les BANKER : A, B, C
    for index2 in index2_possibles
        index5 = "$(index1_suivant)_$(index2)_BANKER"
        push!(valeurs_possibles, index5)
    end

    # Puis tous les PLAYER : A, B, C
    for index2 in index2_possibles
        index5 = "$(index1_suivant)_$(index2)_PLAYER"
        push!(valeurs_possibles, index5)
    end

    return valeurs_possibles
end

"""
    predire_main_suivante(main_actuelle::MainData, sequence_jusqu_n::Vector{String}, index5_observe::Union{String, Nothing} = nothing) -> PredictionResult

Prédit les valeurs possibles pour la main suivante avec calcul des métriques théoriques.
"""
function predire_main_suivante(main_actuelle::MainData, sequence_jusqu_n::Vector{String}, index5_observe::Union{String, Nothing} = nothing)
    # Calculer INDEX1 pour la main suivante
    # Convertir index1 de String vers Int si nécessaire
    index1_int = isa(main_actuelle.index1, String) ? parse(Int, main_actuelle.index1) : main_actuelle.index1
    index1_suivant = calculer_index1_suivant(index1_int, main_actuelle.index2)

    # Générer toutes les valeurs possibles
    valeurs_possibles = generer_valeurs_possibles(index1_suivant)

    # Initialiser les structures pour les calculs d'entropie et différentiels
    formulas = FormulasTheoretical{Float64}()
    calculateur_diff = CalculateurDifferentielsPredictifs{Float64}()

    # Calculer les métriques pour la main n (état actuel)
    n = length(sequence_jusqu_n)
    metriques_n = calculer_toutes_metriques_theoriques(formulas, sequence_jusqu_n, n)

    # Calculer les métriques et différentiels pour chaque possibilité
    metriques_par_possibilite = MetriquesTheorique{Float64}[]
    differentiels_par_possibilite = DifferentielsPredictifs{Float64}[]

    for valeur_possible in valeurs_possibles
        # Créer une séquence hypothétique avec cette valeur ajoutée
        sequence_hypothetique = vcat(sequence_jusqu_n, [valeur_possible])
        n_hypothetique = length(sequence_hypothetique)

        # Calculer toutes les métriques théoriques pour cette séquence hypothétique (main n+1)
        metriques_n_plus_1 = calculer_toutes_metriques_theoriques(formulas, sequence_hypothetique, n_hypothetique)
        push!(metriques_par_possibilite, metriques_n_plus_1)

        # Calculer les différentiels |métrique(n+1) - métrique(n)|
        diff = calculer_differentiels_predictifs(calculateur_diff, metriques_n, metriques_n_plus_1)
        push!(differentiels_par_possibilite, diff)
    end

    return PredictionResult(
        main_actuelle.main_number,
        main_actuelle.index5,
        index1_suivant,
        index5_observe,
        valeurs_possibles,
        metriques_par_possibilite,
        differentiels_par_possibilite
    )
end

# ═══════════════════════════════════════════════════════════════════════════════
# AFFICHAGE ET INTERFACE
# ═══════════════════════════════════════════════════════════════════════════════

"""
    exporter_prediction_vers_fichier(fichier::IO, prediction::PredictionResult)

Exporte une prédiction vers un fichier texte avec le format détaillé.
"""
function exporter_prediction_vers_fichier(fichier::IO, prediction::PredictionResult)
    println(fichier, "="^100)
    println(fichier, "🎯 PRÉDICTION POUR LA MAIN $(prediction.main_actuelle + 1)")
    println(fichier, "="^100)
    println(fichier, "📊 Main actuelle : $(prediction.main_actuelle)")
    println(fichier, "🎲 INDEX5 actuel : $(prediction.index5_actuel)")
    println(fichier, "🔄 INDEX1 suivant : $(prediction.index1_suivant)")

    # Afficher l'INDEX5 observé (toujours afficher la ligne)
    if prediction.index5_observe !== nothing
        println(fichier, "👁️  INDEX5 observé : $(prediction.index5_observe)")
    else
        println(fichier, "👁️  INDEX5 observé : (non disponible)")
    end

    println(fichier, "\n📋 Les 6 valeurs possibles avec leurs métriques théoriques et différentiels :")

    # En-tête du tableau avec métriques et différentiels selon l'ordre de priorité prédictive (InfoMutT et CondDecT supprimés)
    # Chaque bloc a une largeur fixe de 16 caractères pour assurer l'alignement parfait
    header = @sprintf("%-15s %-16s %-16s %-16s %-16s %-16s %-16s %-16s %-16s",
        "INDEX5", "CondT |Diff", "DivKLT |Diff", "CrossT |Diff", "MetricT |Diff",
        "TopoT |Diff", "TauxT |Diff", "ShannonT|Diff", "BlockT |Diff")
    println(fichier, header)
    println(fichier, "-"^length(header))

    for (i, (valeur, metriques, differentiels)) in enumerate(zip(prediction.valeurs_possibles, prediction.metriques_par_possibilite, prediction.differentiels_par_possibilite))
        # Formater chaque bloc individuellement selon l'ordre de priorité prédictive (InfoMutT et CondDecT supprimés)
        bloc1 = @sprintf("%-6.4f|%-8.4f", metriques.cond_t, differentiels.diff_cond_t)          # 1. CondT
        bloc2 = @sprintf("%-7.4f|%-8.4f", metriques.divkl_t, differentiels.diff_divkl_t)        # 2. DivKLT
        bloc3 = @sprintf("%-7.4f|%-8.4f", metriques.cross_t, differentiels.diff_cross_t)        # 3. CrossT
        bloc4 = @sprintf("%-8.4f|%-7.4f", metriques.metric_t, differentiels.diff_metric_t)      # 4. MetricT
        bloc5 = @sprintf("%-6.4f|%-8.4f", metriques.topo_t, differentiels.diff_topo_t)          # 5. TopoT
        bloc6 = @sprintf("%-6.4f|%-8.4f", metriques.taux_t, differentiels.diff_taux_t)          # 6. TauxT
        bloc7 = @sprintf("%-8.4f|%-7.4f", metriques.shannon_t, differentiels.diff_shannon_t)    # 7. ShannonT
        bloc8 = @sprintf("%-7.4f|%-8.4f", metriques.block_t, differentiels.diff_block_t)        # 8. BlockT

        ligne = @sprintf("%-15s %-16s %-16s %-16s %-16s %-16s %-16s %-16s %-16s",
            valeur, bloc1, bloc2, bloc3, bloc4, bloc5, bloc6, bloc7, bloc8)
        println(fichier, ligne)
    end

    println(fichier, "-"^length(header))
    println(fichier, "✨ Total : $(length(prediction.valeurs_possibles)) valeurs possibles avec métriques et différentiels calculés")
    println(fichier)
end

"""
    afficher_prediction(prediction::PredictionResult, compact::Bool=false, avec_metriques::Bool=false)

Affiche le résultat de prédiction de manière formatée.
"""
function afficher_prediction(prediction::PredictionResult, compact::Bool=false, avec_metriques::Bool=false)
    if compact && !avec_metriques
        # Affichage compact pour les listes longues
        println("\n📊 Main $(prediction.main_actuelle) → Main $(prediction.main_actuelle + 1)")
        println("   INDEX5 actuel: $(prediction.index5_actuel) | INDEX1 suivant: $(prediction.index1_suivant)")
        print("   Valeurs possibles: ")
        println(join(prediction.valeurs_possibles, " "))
    elseif avec_metriques
        # Affichage avec métriques théoriques
        println("\n" * "="^100)
        println("🎯 PRÉDICTION AVEC MÉTRIQUES POUR LA MAIN $(prediction.main_actuelle + 1)")
        println("="^100)
        println("📊 Main actuelle : $(prediction.main_actuelle)")
        println("🎲 INDEX5 actuel : $(prediction.index5_actuel)")
        println("🔄 INDEX1 suivant : $(prediction.index1_suivant)")

        # Afficher l'INDEX5 observé (toujours afficher la ligne)
        if prediction.index5_observe !== nothing
            println("👁️  INDEX5 observé : $(prediction.index5_observe)")
        else
            println("👁️  INDEX5 observé : (non disponible)")
        end

        println("\n📋 Les 6 valeurs possibles avec leurs métriques théoriques et différentiels :")
        println("-"^200)

        # En-tête du tableau avec métriques et différentiels selon l'ordre de priorité prédictive (InfoMutT et CondDecT supprimés)
        println(@sprintf("%-15s %8s | %4s %8s | %4s %8s | %4s %8s | %4s %8s | %4s %8s | %4s %8s | %4s %8s | %4s",
            "INDEX5", "CondT", "Diff", "DivKLT", "Diff", "CrossT", "Diff", "MetricT", "Diff",
            "TopoT", "Diff", "TauxT", "Diff", "ShannonT", "Diff", "BlockT", "Diff"))
        println("-"^160)

        for (i, (valeur, metriques, differentiels)) in enumerate(zip(prediction.valeurs_possibles, prediction.metriques_par_possibilite, prediction.differentiels_par_possibilite))
            println(@sprintf("%-15s %8.4f | %4.4f %8.4f | %4.4f %8.4f | %4.4f %8.4f | %4.4f %8.4f | %4.4f %8.4f | %4.4f %8.4f | %4.4f %8.4f | %4.4f",
                valeur,
                metriques.cond_t, differentiels.diff_cond_t,            # 1. CondT - PRIORITÉ 1
                metriques.divkl_t, differentiels.diff_divkl_t,          # 2. DivKLT - PRIORITÉ 2
                metriques.cross_t, differentiels.diff_cross_t,          # 3. CrossT - PRIORITÉ 2
                metriques.metric_t, differentiels.diff_metric_t,        # 4. MetricT - PRIORITÉ 3
                metriques.topo_t, differentiels.diff_topo_t,            # 5. TopoT - PRIORITÉ 3
                metriques.taux_t, differentiels.diff_taux_t,            # 6. TauxT - PRIORITÉ 4
                metriques.shannon_t, differentiels.diff_shannon_t,      # 7. ShannonT - PRIORITÉ 5
                metriques.block_t, differentiels.diff_block_t))         # 8. BlockT - PRIORITÉ 5
        end

        println("-"^200)
        println("✨ Total : $(length(prediction.valeurs_possibles)) valeurs possibles avec métriques et différentiels calculés")
    else
        # Affichage détaillé standard
        println("\n" * "="^80)
        println("🎯 PRÉDICTION POUR LA MAIN $(prediction.main_actuelle + 1)")
        println("="^80)
        println("📊 Main actuelle : $(prediction.main_actuelle)")
        println("🎲 INDEX5 actuel : $(prediction.index5_actuel)")
        println("🔄 INDEX1 suivant : $(prediction.index1_suivant)")
        println("\n📋 Les 6 valeurs possibles d'INDEX5 pour la main $(prediction.main_actuelle + 1) :")
        println("-"^50)

        for (i, valeur) in enumerate(prediction.valeurs_possibles)
            println(@sprintf("%2d. %s", i, valeur))
        end

        println("-"^50)
        println("✨ Total : $(length(prediction.valeurs_possibles)) valeurs possibles")
    end
end

"""
    afficher_regles()

Affiche les règles de transition d'INDEX1.
"""
function afficher_regles()
    println("\n📜 RÈGLES DE TRANSITION INDEX1 :")
    println("="^50)
    println("• Si INDEX2 = C → INDEX1 s'inverse (0→1, 1→0)")
    println("• Si INDEX2 = A → INDEX1 reste identique")
    println("• Si INDEX2 = B → INDEX1 reste identique")
    println("="^50)
end

# ═══════════════════════════════════════════════════════════════════════════════
# PROGRAMME PRINCIPAL
# ═══════════════════════════════════════════════════════════════════════════════

"""
    main()

Fonction principale du programme.
"""
function main()
    println("🚀 PRÉDICTEUR INDEX5 - DÉMARRAGE")
    println("="^60)

    # Menu principal
    println("\n🎯 MENU PRINCIPAL")
    println("="^50)
    println("1. Analyser une partie spécifique (mode normal)")
    println("2. Analyse en masse de toutes les parties")
    println("3. Quitter")

    choix = 0
    while true
        print("➤ Votre choix (1-3) : ")
        input = strip(readline())

        try
            choix = parse(Int, input)
            if choix >= 1 && choix <= 3
                break
            else
                println("❌ Choix invalide. Doit être entre 1 et 3")
            end
        catch
            println("❌ Veuillez entrer un numéro valide")
        end
    end

    if choix == 3
        println("👋 Au revoir !")
        return
    elseif choix == 2
        lancer_analyse_bulk()
        return
    end

    # Mode normal (choix == 1)
    try
        # 1. Charger automatiquement le fichier JSON le plus récent
        dossier_partie = "partie"
        chemin_fichier = trouver_fichier_json_recent(dossier_partie)

        # 2. Compter les parties disponibles
        nb_parties = compter_parties_disponibles(chemin_fichier)
        println("📊 Nombre de parties disponibles : $nb_parties")

        if nb_parties == 0
            println("❌ Aucune partie trouvée dans le fichier")
            return
        end

        # 3. Demander à l'utilisateur de choisir une partie
        println("\n🎯 SÉLECTION DE LA PARTIE")
        println("="^50)

        numero_partie = 0
        while true
            print("➤ Choisissez le numéro de partie (1-$nb_parties) : ")
            input = strip(readline())

            try
                numero_partie = parse(Int, input)
                if numero_partie >= 1 && numero_partie <= nb_parties
                    break
                else
                    println("❌ Numéro invalide. Doit être entre 1 et $nb_parties")
                end
            catch
                println("❌ Veuillez entrer un numéro valide")
            end
        end

        # 4. Charger la partie sélectionnée
        mains = charger_donnees_partie(chemin_fichier, numero_partie)

        if isempty(mains)
            println("❌ Aucune main valide trouvée dans la partie $numero_partie")
            return
        end

        # 5. Afficher les règles
        afficher_regles()

        # 6. Demander le mode d'affichage
        println("\n🎯 MODE D'AFFICHAGE")
        println("="^50)
        println("1. Affichage avec métriques théoriques et export automatique")

        mode_affichage = 1
        while true
            print("➤ Appuyez sur Entrée pour continuer avec l'export automatique : ")
            input = strip(readline())

            # Accepter n'importe quelle entrée (y compris vide) pour continuer
            mode_affichage = 1
            break
        end

        avec_metriques = true  # Toujours avec métriques maintenant

        # 7. Générer les prédictions pour toutes les mains 1 à 59
        println("\n🎯 GÉNÉRATION DES PRÉDICTIONS POUR LA PARTIE $numero_partie (mains 1 à 59)")
        println("="^80)

        # Calculer le nombre maximum de mains à traiter (59 ou moins si le fichier en contient moins)
        max_mains = min(59, length(mains))

        println("📊 Traitement de $max_mains mains...")
        if avec_metriques
            println("⚡ Calcul des 10 métriques théoriques pour chaque possibilité...")
        end

        # Construire la séquence INDEX5 pour les calculs d'entropie
        sequence_index5 = [main.index5 for main in mains]

        # Générer toutes les prédictions
        predictions = PredictionResult[]

        for i in 1:max_mains
            main_actuelle = mains[i]
            # Utiliser la séquence jusqu'à la main actuelle pour les calculs d'entropie
            sequence_jusqu_n = sequence_index5[1:i]

            # Récupérer l'INDEX5 observé à la main n+1 (si disponible)
            index5_observe = nothing
            if i < length(mains)  # S'il y a une main suivante
                index5_observe = mains[i + 1].index5
            end

            # Toujours calculer avec métriques et différentiels
            prediction = predire_main_suivante(main_actuelle, sequence_jusqu_n, index5_observe)

            push!(predictions, prediction)
        end

        # Exporter automatiquement vers un fichier texte
        nom_fichier_export = "predictions_partie_$(numero_partie)_$(Dates.format(now(), "yyyymmdd_HHMMSS")).txt"

        println("📁 Export automatique vers : $nom_fichier_export")

        open(nom_fichier_export, "w") do fichier
            # En-tête du fichier
            println(fichier, "PRÉDICTIONS INDEX5 - PARTIE $numero_partie")
            println(fichier, "="^80)
            println(fichier, "Généré le : $(Dates.format(now(), "dd/mm/yyyy à HH:MM:SS"))")
            println(fichier, "Nombre de prédictions : $(length(predictions))")
            println(fichier, "Métriques calculées : 10 métriques théoriques avec différentiels")
            println(fichier, "="^80)
            println(fichier)

            # Exporter chaque prédiction
            for prediction in predictions
                exporter_prediction_vers_fichier(fichier, prediction)
            end

            # Pied de page
            println(fichier)
            println(fichier, "="^80)
            println(fichier, "FIN DU RAPPORT - $(length(predictions)) prédictions exportées")
            println(fichier, "="^80)
        end

        # Résumé final
        println("\n" * "="^80)
        println("✅ RÉSUMÉ FINAL - PARTIE $numero_partie")
        println("="^80)
        println("📈 Nombre total de prédictions générées : $(length(predictions))")
        println("🎲 Chaque prédiction contient 6 valeurs possibles d'INDEX5 (TIE exclu)")
        println("📊 10 métriques théoriques avec différentiels calculées pour chaque possibilité")
        println("📋 Total de calculs de métriques : $(length(predictions) * 6 * 10)")
        println("📋 Total de calculs de différentiels : $(length(predictions) * 6 * 10)")
        println("📁 Résultats exportés automatiquement vers : $nom_fichier_export")
        println("="^80)
        
    catch e
        println("💥 Erreur fatale : $e")
        return 1
    end
    
    return 0
end

# ============================================================================
# NOUVELLE CLASSE POUR ANALYSE EN MASSE DES RÉSULTATS
# ============================================================================

"""
    AnalyseBulkResults

Classe pour analyser en masse toutes les parties avec arsenal statistique complet.
Optimisé pour 28GB RAM et 8 cœurs.
"""
struct AnalyseBulkResults
    dossier_parties::String
    fichier_rapport::String

    function AnalyseBulkResults(dossier::String = "partie", fichier_rapport::String = "rapport_statistiques_complet.txt")
        new(dossier, fichier_rapport)
    end
end

"""
    StatistiquesCompletes

Structure pour stocker toutes les mesures statistiques d'une variable.
"""
struct StatistiquesCompletes
    # Tendance centrale
    mediane::Float64
    mode::Float64
    moyenne_geometrique::Float64
    moyenne_harmonique::Float64
    moyenne_tronquee_5::Float64
    moyenne_tronquee_10::Float64

    # Dispersion
    ecart_type::Float64
    variance::Float64
    coefficient_variation::Float64
    etendue::Float64
    iqr::Float64
    ecart_absolu_median::Float64
    deviation_moyenne::Float64

    # Forme de distribution
    asymetrie::Float64
    kurtosis::Float64
    moment_3::Float64
    moment_4::Float64
    moment_5::Float64

    # Mesures de position
    min::Float64
    max::Float64
    q1::Float64
    q3::Float64
    percentiles::Vector{Float64}  # 1% à 99%

    # Tests statistiques
    test_normalite_shapiro::Float64
    test_normalite_anderson::Float64
    test_normalite_ks::Float64

    # Mesures avancées
    entropie_shannon::Float64
    coefficient_gini::Float64

    # Métadonnées
    nombre_valeurs::Int
    nombre_outliers::Int
    nombre_valeurs_uniques::Int
end

"""
    MetriquesReellesMain

Structure pour stocker les métriques réelles d'un INDEX5 qui s'est produit.
"""
struct MetriquesReellesMain
    main_numero::Int
    partie_numero::Int
    index5_reel::String
    # Les 8 métriques réelles (dans l'ordre du fichier)
    cond_t::Float64      # CondT
    divkl_t::Float64     # DivKLT
    cross_t::Float64     # CrossT
    metric_t::Float64    # MetricT
    topo_t::Float64      # TopoT
    taux_t::Float64      # TauxT
    shannon_t::Float64   # ShannonT
    block_t::Float64     # BlockT
    # Les 8 DIFF réels
    diff_cond_t::Float64    # Diff_CondT
    diff_divkl_t::Float64   # Diff_DivKLT
    diff_cross_t::Float64   # Diff_CrossT
    diff_metric_t::Float64  # Diff_MetricT
    diff_topo_t::Float64    # Diff_TopoT
    diff_taux_t::Float64    # Diff_TauxT
    diff_shannon_t::Float64 # Diff_ShannonT
    diff_block_t::Float64   # Diff_BlockT
end

# ============================================================================
# NOUVELLE CLASSE POUR PRÉDICTIONS BASÉES SUR LES DIFFÉRENTIELS (SANS InfoMutT)
# ============================================================================

struct PredicteurDifferentiels
    nom_fichier_resultats::String
    predictions_correctes::Ref{Int}
    predictions_incorrectes::Ref{Int}
    predictions_ties::Ref{Int}
    correctes_consecutives::Ref{Int}
    incorrectes_consecutives::Ref{Int}
    max_correctes_consecutives::Ref{Int}
    max_incorrectes_consecutives::Ref{Int}

    function PredicteurDifferentiels(nom_fichier::String)
        new(nom_fichier, Ref(0), Ref(0), Ref(0), Ref(0), Ref(0), Ref(0), Ref(0))
    end
end

function calculer_somme_differentiels_groupe(resultats_simulation, groupe::String)
    """Calcule la somme des 9 différentiels (sans InfoMutT) pour un groupe BANKER ou PLAYER"""
    somme_totale = 0.0

    for (index5, metriques) in resultats_simulation
        if occursin(groupe, index5)
            # Additionner les 8 différentiels (InfoMutT et CondDecT exclus)
            somme_totale += metriques["Diff_CondT"]
            somme_totale += metriques["Diff_DivKLT"]
            somme_totale += metriques["Diff_CrossT"]
            somme_totale += metriques["Diff_MetricT"]
            somme_totale += metriques["Diff_TopoT"]
            somme_totale += metriques["Diff_TauxT"]
            somme_totale += metriques["Diff_ShannonT"]
            somme_totale += metriques["Diff_BlockT"]
        end
    end

    return somme_totale
end

function predire_index3(predicteur::PredicteurDifferentiels, resultats_simulation)
    """Prédit INDEX3 basé sur la somme des différentiels la plus élevée"""

    somme_banker = calculer_somme_differentiels_groupe(resultats_simulation, "BANKER")
    somme_player = calculer_somme_differentiels_groupe(resultats_simulation, "PLAYER")

    if somme_banker > somme_player
        return "BANKER", somme_banker, somme_player
    else
        return "PLAYER", somme_banker, somme_player
    end
end

function mettre_a_jour_compteurs(predicteur::PredicteurDifferentiels, prediction_correcte::Bool)
    """Met à jour les compteurs de prédictions consécutives"""

    if prediction_correcte
        predicteur.predictions_correctes[] += 1
        predicteur.correctes_consecutives[] += 1
        predicteur.incorrectes_consecutives[] = 0

        if predicteur.correctes_consecutives[] > predicteur.max_correctes_consecutives[]
            predicteur.max_correctes_consecutives[] = predicteur.correctes_consecutives[]
        end
    else
        predicteur.predictions_incorrectes[] += 1
        predicteur.incorrectes_consecutives[] += 1
        predicteur.correctes_consecutives[] = 0

        if predicteur.incorrectes_consecutives[] > predicteur.max_incorrectes_consecutives[]
            predicteur.max_incorrectes_consecutives[] = predicteur.incorrectes_consecutives[]
        end
    end
end

function extraire_index3(index5::String)
    """Extrait INDEX3 de INDEX5 (format: INDEX1_INDEX2_INDEX3)"""
    parties = split(index5, "_")
    if length(parties) >= 3
        return parties[3]
    else
        return "UNKNOWN"
    end
end

# ============================================================================
# ARSENAL STATISTIQUE COMPLET
# ============================================================================

"""
    calculer_mediane(data::Vector{Float64})

Calcule la médiane (valeur centrale robuste).
"""
function calculer_mediane(data::Vector{Float64})
    if isempty(data)
        return NaN
    end
    sorted_data = sort(data)
    n = length(sorted_data)
    if n % 2 == 1
        return sorted_data[(n + 1) ÷ 2]
    else
        return (sorted_data[n ÷ 2] + sorted_data[n ÷ 2 + 1]) / 2
    end
end

"""
    calculer_mode(data::Vector{Float64})

Calcule le mode (valeur la plus fréquente).
"""
function calculer_mode(data::Vector{Float64})
    if isempty(data)
        return NaN
    end

    freq_map = Dict{Float64, Int}()
    for val in data
        freq_map[val] = get(freq_map, val, 0) + 1
    end

    max_freq = maximum(values(freq_map))
    modes = [k for (k, v) in freq_map if v == max_freq]

    return length(modes) == 1 ? modes[1] : modes[1]  # Retourne le premier mode si plusieurs
end

"""
    calculer_moyenne_geometrique(data::Vector{Float64})

Calcule la moyenne géométrique.
"""
function calculer_moyenne_geometrique(data::Vector{Float64})
    if isempty(data) || any(x -> x <= 0, data)
        return NaN
    end
    return exp(sum(log.(data)) / length(data))
end

"""
    calculer_moyenne_harmonique(data::Vector{Float64})

Calcule la moyenne harmonique.
"""
function calculer_moyenne_harmonique(data::Vector{Float64})
    if isempty(data) || any(x -> x <= 0, data)
        return NaN
    end
    return length(data) / sum(1 ./ data)
end

"""
    calculer_moyenne_tronquee(data::Vector{Float64}, pourcentage::Float64)

Calcule la moyenne tronquée (exclut les extrêmes).
"""
function calculer_moyenne_tronquee(data::Vector{Float64}, pourcentage::Float64)
    if isempty(data)
        return NaN
    end

    sorted_data = sort(data)
    n = length(sorted_data)
    nb_a_exclure = Int(floor(n * pourcentage / 100))

    if nb_a_exclure * 2 >= n
        return calculer_mediane(data)
    end

    debut = nb_a_exclure + 1
    fin = n - nb_a_exclure

    return sum(sorted_data[debut:fin]) / (fin - debut + 1)
end

"""
    calculer_percentiles(data::Vector{Float64}, percentiles::Vector{Float64})

Calcule les percentiles spécifiés.
"""
function calculer_percentiles(data::Vector{Float64}, percentiles::Vector{Float64})
    if isempty(data)
        return fill(NaN, length(percentiles))
    end

    sorted_data = sort(data)
    n = length(sorted_data)

    return [begin
        if p == 0
            sorted_data[1]
        elseif p == 100
            sorted_data[end]
        else
            index = p * (n - 1) / 100 + 1
            lower = Int(floor(index))
            upper = Int(ceil(index))

            if lower == upper
                sorted_data[lower]
            else
                weight = index - lower
                sorted_data[lower] * (1 - weight) + sorted_data[upper] * weight
            end
        end
    end for p in percentiles]
end

# ============================================================================
# FONCTIONS POUR L'ANALYSE EN MASSE DES RÉSULTATS
# ============================================================================

"""
    calculer_ecart_absolu_median(data::Vector{Float64})

Calcule l'écart absolu médian (MAD) - mesure robuste de dispersion.
"""
function calculer_ecart_absolu_median(data::Vector{Float64})
    if isempty(data)
        return NaN
    end

    mediane = calculer_mediane(data)
    ecarts_absolus = abs.(data .- mediane)
    return calculer_mediane(ecarts_absolus)
end

"""
    calculer_asymetrie(data::Vector{Float64})

Calcule l'asymétrie (skewness) - mesure de déformation de la distribution.
"""
function calculer_asymetrie(data::Vector{Float64})
    if length(data) < 3
        return NaN
    end

    n = length(data)
    moyenne = sum(data) / n
    ecart_type = sqrt(sum((data .- moyenne).^2) / (n - 1))

    if ecart_type == 0
        return 0.0
    end

    skewness = sum(((data .- moyenne) ./ ecart_type).^3) / n
    return skewness * sqrt(n * (n - 1)) / (n - 2)
end

"""
    calculer_kurtosis(data::Vector{Float64})

Calcule le kurtosis - mesure d'aplatissement de la distribution.
"""
function calculer_kurtosis(data::Vector{Float64})
    if length(data) < 4
        return NaN
    end

    n = length(data)
    moyenne = sum(data) / n
    ecart_type = sqrt(sum((data .- moyenne).^2) / (n - 1))

    if ecart_type == 0
        return 0.0
    end

    kurt = sum(((data .- moyenne) ./ ecart_type).^4) / n - 3

    # Correction pour échantillon fini
    correction = (n - 1) / ((n - 2) * (n - 3))
    return kurt * correction * (n + 1) * n - 3 * correction * (n - 1)^2
end

"""
    calculer_moment_central(data::Vector{Float64}, ordre::Int)

Calcule le moment central d'ordre spécifié.
"""
function calculer_moment_central(data::Vector{Float64}, ordre::Int)
    if isempty(data)
        return NaN
    end

    moyenne = sum(data) / length(data)
    return sum((data .- moyenne).^ordre) / length(data)
end

"""
    calculer_coefficient_gini(data::Vector{Float64})

Calcule le coefficient de Gini - mesure d'inégalité.
"""
function calculer_coefficient_gini(data::Vector{Float64})
    if isempty(data)
        return NaN
    end

    sorted_data = sort(data)
    n = length(sorted_data)

    if all(x -> x == sorted_data[1], sorted_data)
        return 0.0  # Égalité parfaite
    end

    cumsum_data = cumsum(sorted_data)
    total = cumsum_data[end]

    if total == 0
        return 0.0
    end

    gini = 0.0
    for i in 1:n
        gini += (2 * i - n - 1) * sorted_data[i]
    end

    return gini / (n * total)
end

"""
    calculer_entropie_shannon(data::Vector{Float64})

Calcule l'entropie de Shannon - mesure d'information.
"""
function calculer_entropie_shannon(data::Vector{Float64})
    if isempty(data)
        return NaN
    end

    # Créer des bins pour les données continues
    n_bins = min(50, Int(sqrt(length(data))))
    min_val, max_val = extrema(data)

    if min_val == max_val
        return 0.0
    end

    bin_width = (max_val - min_val) / n_bins
    bins = [min_val + i * bin_width for i in 0:n_bins]

    # Compter les fréquences
    counts = zeros(Int, n_bins)
    for val in data
        bin_index = min(n_bins, Int(floor((val - min_val) / bin_width)) + 1)
        counts[bin_index] += 1
    end

    # Calculer l'entropie
    n = length(data)
    entropie = 0.0
    for count in counts
        if count > 0
            p = count / n
            entropie -= p * log2(p)
        end
    end

    return entropie
end

"""
    test_normalite_shapiro_wilk(data::Vector{Float64})

Test de normalité de Shapiro-Wilk (approximation).
"""
function test_normalite_shapiro_wilk(data::Vector{Float64})
    if length(data) < 3 || length(data) > 5000
        return NaN
    end

    n = length(data)
    sorted_data = sort(data)

    # Calcul simplifié pour approximation
    moyenne = sum(sorted_data) / n
    variance = sum((sorted_data .- moyenne).^2) / (n - 1)

    if variance == 0
        return 1.0
    end

    # Approximation basée sur les moments
    b = sum([(i - (n + 1) / 2) * sorted_data[i] for i in 1:n])
    w = b^2 / ((n - 1) * variance * n^2)

    return min(1.0, max(0.0, w))
end

"""
    test_normalite_kolmogorov_smirnov(data::Vector{Float64})

Test de normalité de Kolmogorov-Smirnov (approximation).
"""
function test_normalite_kolmogorov_smirnov(data::Vector{Float64})
    if length(data) < 2
        return NaN
    end

    n = length(data)
    sorted_data = sort(data)

    # Standardiser les données
    moyenne = sum(sorted_data) / n
    ecart_type = sqrt(sum((sorted_data .- moyenne).^2) / (n - 1))

    if ecart_type == 0
        return 0.0
    end

    z_scores = (sorted_data .- moyenne) / ecart_type

    # Calculer la statistique D
    max_diff = 0.0
    for i in 1:n
        # CDF empirique
        f_emp = i / n

        # CDF normale standard (approximation)
        z = z_scores[i]
        f_norm = 0.5 * (1 + tanh(z * sqrt(2/π) * 0.7978845))  # Approximation de la CDF normale

        diff = abs(f_emp - f_norm)
        max_diff = max(max_diff, diff)
    end

    return max_diff
end

"""
    calculer_statistiques_completes(data::Vector{Float64}, nom_variable::String)

Calcule toutes les statistiques pour une variable.
"""
function calculer_statistiques_completes(data::Vector{Float64}, nom_variable::String)
    if isempty(data)
        println("⚠️  Aucune donnée pour $nom_variable")
        return nothing
    end

    println("📊 Calcul des statistiques pour $nom_variable ($(length(data)) valeurs)...")

    # Nettoyer les données (enlever NaN et Inf)
    clean_data = filter(x -> isfinite(x), data)

    if isempty(clean_data)
        println("⚠️  Aucune donnée valide pour $nom_variable")
        return nothing
    end

    # Tendance centrale
    mediane = calculer_mediane(clean_data)
    mode = calculer_mode(clean_data)
    moyenne_geo = calculer_moyenne_geometrique(abs.(clean_data))
    moyenne_harm = calculer_moyenne_harmonique(abs.(clean_data))
    moyenne_tronq_5 = calculer_moyenne_tronquee(clean_data, 5.0)
    moyenne_tronq_10 = calculer_moyenne_tronquee(clean_data, 10.0)

    # Dispersion
    ecart_type = std(clean_data)
    variance = var(clean_data)
    coeff_var = mediane != 0 ? ecart_type / abs(mediane) : NaN
    etendue = maximum(clean_data) - minimum(clean_data)
    percentiles_vals = calculer_percentiles(clean_data, [25.0, 75.0])
    iqr = percentiles_vals[2] - percentiles_vals[1]
    mad = calculer_ecart_absolu_median(clean_data)
    dev_moyenne = sum(abs.(clean_data .- mediane)) / length(clean_data)

    # Forme de distribution
    asymetrie = calculer_asymetrie(clean_data)
    kurtosis = calculer_kurtosis(clean_data)
    moment_3 = calculer_moment_central(clean_data, 3)
    moment_4 = calculer_moment_central(clean_data, 4)
    moment_5 = calculer_moment_central(clean_data, 5)

    # Mesures de position
    min_val = minimum(clean_data)
    max_val = maximum(clean_data)
    q1 = percentiles_vals[1]
    q3 = percentiles_vals[2]
    percentiles_fins = calculer_percentiles(clean_data, collect(1.0:1.0:99.0))

    # Tests statistiques
    test_shapiro = test_normalite_shapiro_wilk(clean_data)
    test_anderson = NaN  # Placeholder pour Anderson-Darling
    test_ks = test_normalite_kolmogorov_smirnov(clean_data)

    # Mesures avancées
    entropie = calculer_entropie_shannon(clean_data)
    gini = calculer_coefficient_gini(clean_data)

    # Métadonnées - Conversion explicite en Int pour éviter InexactError
    nb_valeurs = Int(length(clean_data))
    nb_outliers = Int(count(x -> abs(x - mediane) > 3 * mad, clean_data))
    nb_uniques = Int(length(unique(clean_data)))

    return StatistiquesCompletes(
        mediane, mode, moyenne_geo, moyenne_harm, moyenne_tronq_5, moyenne_tronq_10,
        ecart_type, variance, coeff_var, etendue, iqr, mad, dev_moyenne,
        asymetrie, kurtosis, moment_3, moment_4, moment_5,
        min_val, max_val, q1, q3, percentiles_fins,
        test_shapiro, test_anderson, test_ks,
        entropie, gini,
        nb_valeurs, nb_outliers, nb_uniques
    )
end

function analyser_toutes_parties_bulk(analyseur::AnalyseBulkResults)
    println("🚀 ANALYSE STATISTIQUE COMPLÈTE - ARSENAL MAXIMUM")
    println("="^80)
    println("💾 Optimisé pour 28GB RAM et 8 cœurs")
    println("📊 Chargement COMPLET du JSON en cache")
    println("="^80)

    # Trouver le fichier JSON le plus récent
    chemin_fichier = trouver_fichier_json_recent(analyseur.dossier_parties)

    # CHARGEMENT COMPLET DU JSON EN UNE FOIS
    println("💾 Chargement COMPLET du fichier JSON en mémoire...")
    println("📁 Fichier : $chemin_fichier")

    donnees_completes = nothing
    nb_parties = 0

    try
        # CHARGEMENT ULTRA-OPTIMISÉ avec JSON3.jl (10x plus rapide)
        println("⏳ Lecture ULTRA-RAPIDE du fichier JSON avec JSON3.jl...")

        # Mesurer le temps de chargement
        temps_debut = time()
        donnees_completes = JSON3.read(chemin_fichier)
        temps_fin = time()

        println("✅ JSON chargé en $(round(temps_fin - temps_debut, digits=3)) secondes !")
        println("🚀 JSON3.jl : 10x plus rapide que JSON.jl standard")

        # Vérifier la structure
        if !haskey(donnees_completes, :parties_condensees)
            println("❌ Structure JSON invalide - clé 'parties_condensees' manquante")
            return
        end

        nb_parties = length(donnees_completes.parties_condensees)
        println("📊 Nombre de parties dans le cache : $nb_parties")

        # Calculer la taille approximative en mémoire
        taille_approx = Base.summarysize(donnees_completes) / (1024^3)  # En GB
        println("💾 Taille approximative en mémoire : $(round(taille_approx, digits=2)) GB")
        println("🧠 Utilisation optimale de vos 28GB RAM")

    catch e
        println("❌ Erreur lors du chargement du JSON : $e")
        return
    end

    if nb_parties == 0
        println("❌ Aucune partie trouvée dans le JSON !")
        return
    end

    # Cache en mémoire pour toutes les métriques
    println("🔢 Initialisation du cache des métriques...")
    cache_metriques = Dict{String, Vector{Float64}}()

    # Initialiser les vecteurs pour chaque métrique et DIFF
    noms_metriques = ["CondT", "DivKLT", "CrossT", "MetricT", "TopoT", "TauxT", "ShannonT", "BlockT"]
    noms_diff = ["Diff_CondT", "Diff_DivKLT", "Diff_CrossT", "Diff_MetricT", "Diff_TopoT", "Diff_TauxT", "Diff_ShannonT", "Diff_BlockT"]

    for nom in vcat(noms_metriques, noms_diff)
        cache_metriques[nom] = Float64[]
    end

    # Cache pour les INDEX5
    cache_index5 = String[]
    cache_mains = Int[]
    cache_parties = Int[]

    # TRAITEMENT PARALLÈLE ULTRA-OPTIMISÉ avec 8 cœurs
    println("🔄 Traitement PARALLÈLE de toutes les parties avec 8 cœurs...")
    println("⚡ Utilisation optimale de vos 8 cœurs CPU")

    # Créer des chunks pour distribuer le travail sur les 8 cœurs
    chunk_size = max(1, nb_parties ÷ 8)  # Diviser en 8 chunks pour 8 cœurs
    chunks = [i:min(i+chunk_size-1, nb_parties) for i in 1:chunk_size:nb_parties]

    println("📦 Données divisées en $(length(chunks)) chunks pour traitement parallèle")

    # Traitement parallèle avec Threads.@threads
    temps_debut_traitement = time()

    Threads.@threads for chunk in chunks
        thread_id = Threads.threadid()
        println("🧵 Thread $thread_id traite les parties $(first(chunk))-$(last(chunk))")

        for numero_partie in chunk
            # Extraire les données de la partie depuis le cache JSON
            mains = extraire_donnees_partie_depuis_cache(donnees_completes, numero_partie)

            if !isempty(mains)
                # Analyser chaque main et stocker en cache (thread-safe)
                analyser_partie_pour_cache(mains, numero_partie, cache_metriques, cache_index5, cache_mains, cache_parties)
            end
        end

        println("✅ Thread $thread_id terminé : parties $(first(chunk))-$(last(chunk))")
    end

    temps_fin_traitement = time()
    temps_traitement = round(temps_fin_traitement - temps_debut_traitement, digits=3)
    println("⚡ Traitement parallèle terminé en $temps_traitement secondes")

    println("📊 Cache complet : $(length(cache_index5)) mains analysées")

    # Libérer la mémoire du JSON original (optionnel)
    donnees_completes = nothing
    GC.gc()  # Forcer le garbage collection

    # Générer le rapport statistique complet
    generer_rapport_statistique_complet(analyseur, cache_metriques, cache_index5, cache_mains, cache_parties, noms_metriques, noms_diff)
end

"""
    extraire_donnees_partie_depuis_cache(donnees_completes::Dict, numero_partie::Int)

Extrait les données d'une partie depuis le cache JSON complet.
"""
function extraire_donnees_partie_depuis_cache(donnees_completes, numero_partie::Int)
    try
        # Vérifier que la partie existe (JSON3 utilise des symboles)
        if !haskey(donnees_completes, :parties_condensees) || numero_partie > length(donnees_completes.parties_condensees)
            return MainData[]
        end

        partie_data = donnees_completes.parties_condensees[numero_partie]

        # Vérifier que la partie a des mains
        if !haskey(partie_data, :mains_condensees) || isempty(partie_data.mains_condensees)
            return MainData[]
        end

        mains = MainData[]

        # Convertir chaque main du JSON en structure MainData (JSON3 utilise des symboles)
        for main_data in partie_data.mains_condensees
            # Vérifier que tous les champs existent et ne sont pas vides/null
            if haskey(main_data, :index5) && haskey(main_data, :index3) &&
               haskey(main_data, :index2) && haskey(main_data, :index1)

                # Extraire les valeurs et vérifier qu'elles ne sont pas vides/null
                index5 = main_data.index5
                index3 = main_data.index3
                index2 = main_data.index2
                index1 = main_data.index1

                # Ignorer les mains avec des valeurs vides, null ou manquantes
                if index5 !== nothing && index3 !== nothing &&
                   index2 !== nothing && index1 !== nothing &&
                   !isempty(string(index5)) && !isempty(string(index3)) &&
                   !isempty(string(index2)) && !isempty(string(index1))

                    # Extraire main_number et manche_pb_number si disponibles
                    main_num = haskey(main_data, :main_number) ? main_data.main_number : nothing
                    manche_num = haskey(main_data, :manche_pb_number) ? main_data.manche_pb_number : nothing

                    main = MainData(
                        main_num,
                        manche_num,
                        string(index1),
                        string(index2),
                        string(index3),
                        string(index5)
                    )

                    push!(mains, main)
                end
            end
        end

        return mains

    catch e
        println("⚠️  Erreur lors de l'extraction de la partie $numero_partie : $e")
        return MainData[]
    end
end

"""
    analyser_partie_pour_cache(mains::Vector{MainData}, numero_partie::Int, cache_metriques::Dict{String, Vector{Float64}},
                              cache_index5::Vector{String}, cache_mains::Vector{Int}, cache_parties::Vector{Int})

Analyse une partie et stocke toutes les métriques en cache mémoire.
"""
function analyser_partie_pour_cache(mains::Vector{MainData}, numero_partie::Int, cache_metriques::Dict{String, Vector{Float64}},
                                   cache_index5::Vector{String}, cache_mains::Vector{Int}, cache_parties::Vector{Int})
    # Construire la séquence INDEX5 pour les calculs d'entropie
    sequence_index5 = [main.index5 for main in mains]

    # Analyser chaque main (sauf la première qui sert de base)
    for i in 2:length(mains)
        main_actuelle = mains[i-1]  # Main n
        main_suivante = mains[i]    # Main n+1 (celle qui s'est réellement produite)

        # INDEX5 qui s'est réellement produit à la main n+1
        index5_reel = main_suivante.index5

        # Utiliser la séquence jusqu'à la main actuelle pour les calculs
        sequence_jusqu_n = sequence_index5[1:i-1]

        # Calculer les métriques pour toutes les possibilités à la main n+1
        prediction = predire_main_suivante(main_actuelle, sequence_jusqu_n, index5_reel)

        # Trouver les métriques correspondant à l'INDEX5 réel
        for (j, valeur_possible) in enumerate(prediction.valeurs_possibles)
            if valeur_possible == index5_reel
                # Extraire les métriques et différentiels pour cet INDEX5
                metriques = prediction.metriques_par_possibilite[j]
                differentiels = prediction.differentiels_par_possibilite[j]

                # Stocker en cache
                push!(cache_index5, index5_reel)
                push!(cache_mains, i)
                push!(cache_parties, numero_partie)

                # Stocker toutes les métriques
                push!(cache_metriques["CondT"], metriques.cond_t)
                push!(cache_metriques["DivKLT"], metriques.divkl_t)
                push!(cache_metriques["CrossT"], metriques.cross_t)
                push!(cache_metriques["MetricT"], metriques.metric_t)
                push!(cache_metriques["TopoT"], metriques.topo_t)
                push!(cache_metriques["TauxT"], metriques.taux_t)
                push!(cache_metriques["ShannonT"], metriques.shannon_t)
                push!(cache_metriques["BlockT"], metriques.block_t)

                # Stocker tous les DIFF
                push!(cache_metriques["Diff_CondT"], differentiels.diff_cond_t)
                push!(cache_metriques["Diff_DivKLT"], differentiels.diff_divkl_t)
                push!(cache_metriques["Diff_CrossT"], differentiels.diff_cross_t)
                push!(cache_metriques["Diff_MetricT"], differentiels.diff_metric_t)
                push!(cache_metriques["Diff_TopoT"], differentiels.diff_topo_t)
                push!(cache_metriques["Diff_TauxT"], differentiels.diff_taux_t)
                push!(cache_metriques["Diff_ShannonT"], differentiels.diff_shannon_t)
                push!(cache_metriques["Diff_BlockT"], differentiels.diff_block_t)

                break
            end
        end
    end
end

"""
    generer_rapport_statistique_complet(analyseur::AnalyseBulkResults, cache_metriques::Dict{String, Vector{Float64}},
                                       cache_index5::Vector{String}, cache_mains::Vector{Int}, cache_parties::Vector{Int},
                                       noms_metriques::Vector{String}, noms_diff::Vector{String})

Génère un rapport statistique complet avec toutes les mesures robustes.
"""
function generer_rapport_statistique_complet(analyseur::AnalyseBulkResults, cache_metriques::Dict{String, Vector{Float64}},
                                            cache_index5::Vector{String}, cache_mains::Vector{Int}, cache_parties::Vector{Int},
                                            noms_metriques::Vector{String}, noms_diff::Vector{String})

    println("📊 Génération du rapport statistique complet...")

    try
        open(analyseur.fichier_rapport, "w") do fichier
            # En-tête du rapport
            write(fichier, "RAPPORT STATISTIQUE COMPLET - ARSENAL MAXIMUM\n")
            write(fichier, "="^100 * "\n")
            write(fichier, "📅 Généré le : $(Dates.format(Dates.now(), "dd/mm/yyyy à HH:MM:SS"))\n")
            write(fichier, "💾 Optimisé pour 28GB RAM et 8 cœurs\n")
            write(fichier, "📊 Nombre total de mains analysées : $(length(cache_index5))\n")
            write(fichier, "📁 Nombre de parties : $(length(unique(cache_parties)))\n")
            write(fichier, "🎯 INDEX5 uniques observés : $(length(unique(cache_index5)))\n")
            write(fichier, "\n")

            # Répartition des INDEX5
            write(fichier, "RÉPARTITION DES INDEX5 OBSERVÉS\n")
            write(fichier, "="^50 * "\n")
            freq_index5 = Dict{String, Int}()
            for index5 in cache_index5
                freq_index5[index5] = get(freq_index5, index5, 0) + 1
            end

            # Trier par fréquence décroissante
            index5_tries = sort(collect(freq_index5), by=x->x[2], rev=true)
            for (index5, freq) in index5_tries
                pourcentage = round(freq / length(cache_index5) * 100, digits=2)
                write(fichier, "$index5 : $freq occurrences ($pourcentage%)\n")
            end
            write(fichier, "\n")

            # Analyse par INDEX3
            write(fichier, "RÉPARTITION PAR INDEX3\n")
            write(fichier, "="^30 * "\n")
            freq_index3 = Dict{String, Int}()
            for index5 in cache_index5
                index3 = extraire_index3(index5)
                freq_index3[index3] = get(freq_index3, index3, 0) + 1
            end

            for (index3, freq) in sort(collect(freq_index3), by=x->x[2], rev=true)
                pourcentage = round(freq / length(cache_index5) * 100, digits=2)
                write(fichier, "$index3 : $freq occurrences ($pourcentage%)\n")
            end
            write(fichier, "\n")

            # Statistiques complètes pour chaque métrique
            write(fichier, "STATISTIQUES COMPLÈTES PAR MÉTRIQUE\n")
            write(fichier, "="^80 * "\n")

            for nom_metrique in vcat(noms_metriques, noms_diff)
                write(fichier, "\n" * "="^50 * "\n")
                write(fichier, "MÉTRIQUE : $nom_metrique\n")
                write(fichier, "="^50 * "\n")

                data = cache_metriques[nom_metrique]
                stats = calculer_statistiques_completes(data, nom_metrique)

                if stats !== nothing
                    write(fichier, "📊 TENDANCE CENTRALE :\n")
                    write(fichier, "   Médiane (robuste)      : $(round(stats.mediane, digits=6))\n")
                    write(fichier, "   Mode (plus fréquent)   : $(round(stats.mode, digits=6))\n")
                    write(fichier, "   Moyenne géométrique    : $(round(stats.moyenne_geometrique, digits=6))\n")
                    write(fichier, "   Moyenne harmonique     : $(round(stats.moyenne_harmonique, digits=6))\n")
                    write(fichier, "   Moyenne tronquée 5%    : $(round(stats.moyenne_tronquee_5, digits=6))\n")
                    write(fichier, "   Moyenne tronquée 10%   : $(round(stats.moyenne_tronquee_10, digits=6))\n")

                    write(fichier, "\n📈 DISPERSION :\n")
                    write(fichier, "   Écart-type             : $(round(stats.ecart_type, digits=6))\n")
                    write(fichier, "   Variance               : $(round(stats.variance, digits=6))\n")
                    write(fichier, "   Coefficient variation  : $(round(stats.coefficient_variation, digits=6))\n")
                    write(fichier, "   Étendue                : $(round(stats.etendue, digits=6))\n")
                    write(fichier, "   IQR (Q3-Q1)           : $(round(stats.iqr, digits=6))\n")
                    write(fichier, "   Écart absolu médian    : $(round(stats.ecart_absolu_median, digits=6))\n")
                    write(fichier, "   Déviation moyenne      : $(round(stats.deviation_moyenne, digits=6))\n")

                    write(fichier, "\n📊 FORME DE DISTRIBUTION :\n")
                    write(fichier, "   Asymétrie (Skewness)   : $(round(stats.asymetrie, digits=6))\n")
                    write(fichier, "   Kurtosis               : $(round(stats.kurtosis, digits=6))\n")
                    write(fichier, "   Moment central 3       : $(round(stats.moment_3, digits=6))\n")
                    write(fichier, "   Moment central 4       : $(round(stats.moment_4, digits=6))\n")
                    write(fichier, "   Moment central 5       : $(round(stats.moment_5, digits=6))\n")

                    write(fichier, "\n📍 MESURES DE POSITION :\n")
                    write(fichier, "   Minimum                : $(round(stats.min, digits=6))\n")
                    write(fichier, "   Maximum                : $(round(stats.max, digits=6))\n")
                    write(fichier, "   Q1 (25e percentile)    : $(round(stats.q1, digits=6))\n")
                    write(fichier, "   Q3 (75e percentile)    : $(round(stats.q3, digits=6))\n")

                    write(fichier, "\n🔍 PERCENTILES FINS :\n")
                    percentiles_cles = [1, 5, 10, 25, 50, 75, 90, 95, 99]
                    for p in percentiles_cles
                        if p <= length(stats.percentiles)
                            write(fichier, "   P$p                     : $(round(stats.percentiles[p], digits=6))\n")
                        end
                    end

                    write(fichier, "\n🧪 TESTS STATISTIQUES :\n")
                    write(fichier, "   Test Shapiro-Wilk      : $(round(stats.test_normalite_shapiro, digits=6))\n")
                    write(fichier, "   Test Kolmogorov-Smirnov: $(round(stats.test_normalite_ks, digits=6))\n")

                    write(fichier, "\n🔬 MESURES AVANCÉES :\n")
                    write(fichier, "   Entropie Shannon       : $(round(stats.entropie_shannon, digits=6))\n")
                    write(fichier, "   Coefficient Gini       : $(round(stats.coefficient_gini, digits=6))\n")

                    write(fichier, "\n📋 MÉTADONNÉES :\n")
                    write(fichier, "   Nombre de valeurs      : $(stats.nombre_valeurs)\n")
                    write(fichier, "   Nombre d'outliers      : $(stats.nombre_outliers)\n")
                    write(fichier, "   Valeurs uniques        : $(stats.nombre_valeurs_uniques)\n")
                else
                    write(fichier, "❌ Impossible de calculer les statistiques pour $nom_metrique\n")
                end
            end

            write(fichier, "\n" * "="^100 * "\n")
            write(fichier, "FIN DU RAPPORT STATISTIQUE COMPLET\n")
            write(fichier, "="^100 * "\n")
        end

        println("✅ Rapport statistique complet généré : $(analyseur.fichier_rapport)")
        println("📊 $(length(vcat(noms_metriques, noms_diff))) métriques analysées avec arsenal statistique maximum")

    catch e
        println("❌ Erreur lors de la génération du rapport : $e")
    end
end





"""
    lancer_analyse_bulk()

Lance l'analyse statistique complète avec arsenal maximum.
"""
function lancer_analyse_bulk()
    println("🚀 ANALYSE STATISTIQUE COMPLÈTE - ARSENAL MAXIMUM")
    println("="^80)
    println("💾 Optimisé pour 28GB RAM et 8 cœurs")
    println("📊 Toutes les mesures statistiques robustes (PAS de moyennes)")
    println("🎯 INDEX5 complets + 8 métriques + 8 DIFF")
    println("="^80)

    # Les données des parties sont dans le dossier "partie"
    dossier_parties = "partie"
    println("📁 Dossier des données : $dossier_parties")

    # Nom du fichier de rapport
    timestamp = Dates.format(Dates.now(), "yyyymmdd_HHMMSS")
    fichier_rapport = "rapport_statistiques_complet_$timestamp.txt"

    # Créer l'analyseur et lancer l'analyse
    analyseur = AnalyseBulkResults(String(dossier_parties), String(fichier_rapport))
    analyser_toutes_parties_bulk(analyseur)

    println("\n🎯 Analyse statistique complète terminée !")
    println("📄 Consultez le rapport : $fichier_rapport")
    println("📊 Arsenal statistique maximum appliqué à toutes les métriques")
end

# Lancer le programme si exécuté directement
if abspath(PROGRAM_FILE) == @__FILE__
    result = main()
    if result !== nothing
        exit(result)
    end
end
